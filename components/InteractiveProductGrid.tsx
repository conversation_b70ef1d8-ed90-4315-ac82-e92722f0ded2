'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import { ProductFilters } from './ProductFilters';

interface InteractiveProductGridProps {
  initialProducts: ProductWithDetails[];
  categories: Category[];
  locale: Locale;
}

/**
 * مكون تفاعلي لعرض المنتجات مع الفلاتر
 * يعمل client-side فقط ولا يؤثر على SEO
 */
const InteractiveProductGrid: React.FC<InteractiveProductGridProps> = ({
  initialProducts,
  categories, // قد نحتاجه لاحقاً
  locale
}) => {
  const [products] = useState<ProductWithDetails[]>(initialProducts);
  const [filteredProducts, setFilteredProducts] = useState<ProductWithDetails[]>(initialProducts);
  const [filters] = useState<ProductFilters>({});
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const PRODUCTS_PER_PAGE = 12;

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = [...products];

    // فلتر حسب الفئة
    if (filters.category) {
      filtered = filtered.filter(product => product.category_id === filters.category);
    }

    // فلتر حسب المادة
    if (filters.material && filters.material.length > 0) {
      filtered = filtered.filter(product =>
        filters.material?.some(material => {
          // البحث في المواصفات عن المادة
          const materialSpec = product.specifications?.find(spec =>
            spec.spec_key.toLowerCase() === 'material' ||
            spec.spec_key_ar.toLowerCase() === 'المادة'
          );
          return materialSpec && (
            materialSpec.spec_value.toLowerCase().includes(material.toLowerCase()) ||
            materialSpec.spec_value_ar.toLowerCase().includes(material.toLowerCase())
          );
        })
      );
    }

    // فلتر حسب الحجم
    if (filters.size && filters.size.length > 0) {
      filtered = filtered.filter(product =>
        filters.size?.some(size => {
          // البحث في المواصفات عن الحجم
          const sizeSpec = product.specifications?.find(spec =>
            spec.spec_key.toLowerCase() === 'size' ||
            spec.spec_key_ar.toLowerCase() === 'الحجم'
          );
          return sizeSpec && (
            sizeSpec.spec_value.toLowerCase().includes(size.toLowerCase()) ||
            sizeSpec.spec_value_ar.toLowerCase().includes(size.toLowerCase())
          );
        })
      );
    }

    // فلتر حسب الشكل
    if (filters.shape && filters.shape.length > 0) {
      filtered = filtered.filter(product =>
        filters.shape?.some(shape => {
          // البحث في المواصفات عن الشكل
          const shapeSpec = product.specifications?.find(spec =>
            spec.spec_key.toLowerCase() === 'shape' ||
            spec.spec_key_ar.toLowerCase() === 'الشكل'
          );
          return shapeSpec && (
            shapeSpec.spec_value.toLowerCase().includes(shape.toLowerCase()) ||
            shapeSpec.spec_value_ar.toLowerCase().includes(shape.toLowerCase())
          );
        })
      );
    }

    // فلتر حسب السعر
    if (filters.priceRange) {
      filtered = filtered.filter(product => {
        const price = product.price || 0;
        return price >= filters.priceRange!.min && price <= filters.priceRange!.max;
      });
    }

    // فلتر حسب البحث
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(product =>
        (product.title?.toLowerCase().includes(searchTerm)) ||
        (product.title_ar?.toLowerCase().includes(searchTerm)) ||
        (product.description?.toLowerCase().includes(searchTerm)) ||
        (product.description_ar?.toLowerCase().includes(searchTerm))
      );
    }

    // ترتيب النتائج
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let comparison = 0;
        
        switch (filters.sortBy) {
          case 'name':
            const nameA = locale === 'ar' ? (a.title_ar || a.title) : a.title;
            const nameB = locale === 'ar' ? (b.title_ar || b.title) : b.title;
            comparison = nameA.localeCompare(nameB);
            break;
          case 'price':
            const priceA = a.price || 0;
            const priceB = b.price || 0;
            comparison = priceA - priceB;
            break;
          case 'newest':
            comparison = new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
            break;
          case 'popular':
            // يمكن إضافة منطق الشعبية هنا
            comparison = 0;
            break;
        }
        
        return filters.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    setFilteredProducts(filtered);
    setCurrentPage(1);
    setHasMore(filtered.length > PRODUCTS_PER_PAGE);
  }, [filters, products, locale]);

  // تحميل المزيد من المنتجات
  const loadMore = async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      // محاكاة تحميل المزيد من المنتجات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const nextPage = currentPage + 1;
      const startIndex = (nextPage - 1) * PRODUCTS_PER_PAGE;
      const endIndex = startIndex + PRODUCTS_PER_PAGE;
      
      if (startIndex >= filteredProducts.length) {
        setHasMore(false);
      } else {
        setCurrentPage(nextPage);
        setHasMore(endIndex < filteredProducts.length);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
    } finally {
      setLoading(false);
    }
  };

  // المنتجات المعروضة حالياً
  const displayedProducts = filteredProducts.slice(0, currentPage * PRODUCTS_PER_PAGE);

  return (
    <div className="space-y-6">
      {/* نتائج البحث */}
      <div className="flex items-center justify-between">
        <div className="text-gray-600">
          {locale === 'ar' 
            ? `عرض ${displayedProducts.length} من ${filteredProducts.length} منتج`
            : `Showing ${displayedProducts.length} of ${filteredProducts.length} products`
          }
        </div>
        
        {filteredProducts.length === 0 && (
          <div className="text-center py-12 w-full">
            <div className="text-gray-500 text-lg">
              {locale === 'ar' ? 'لا توجد منتجات تطابق البحث' : 'No products match your search'}
            </div>
          </div>
        )}
      </div>

      {/* شبكة المنتجات */}
      {filteredProducts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {displayedProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-300 group">
              {product.images && product.images.length > 0 && (
                <div className="w-full h-48 overflow-hidden">
                  <img
                    src={product.images[0].image_url}
                    alt={locale === 'ar' ? product.title_ar || product.title : product.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                  />
                </div>
              )}
              <div className="p-4">
                <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                  {locale === 'ar' ? product.title_ar || product.title : product.title}
                </h3>
                {product.description && (
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {locale === 'ar' ? product.description_ar || product.description : product.description}
                  </p>
                )}
                <div className="flex items-center justify-between">
                  {product.price && (
                    <span className="text-lg font-bold text-primary">
                      {product.price.toFixed(2)} {locale === 'ar' ? 'ريال' : 'SAR'}
                    </span>
                  )}
                  <a
                    href={`/${locale}/product/${product.id}`}
                    className="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-primary-dark transition-colors"
                  >
                    {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* زر تحميل المزيد */}
      {hasMore && filteredProducts.length > 0 && (
        <div className="text-center">
          <button
            onClick={loadMore}
            disabled={loading}
            className="bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
              </div>
            ) : (
              locale === 'ar' ? 'تحميل المزيد' : 'Load More'
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default InteractiveProductGrid;
